def main():
    from deepsearcher.configuration import Configuration, init_config
    from deepsearcher.online_query import query

    config = Configuration()

    # Customize your config here,
    # more configuration see the Configuration Details section below.
    # config.set_provider_config("llm", "OpenAI", {"model": "o1-mini"})
    config.set_provider_config("llm", "DeepSeek", {"model": "deepseek-reasoner"})

    # 使用MilvusEmbedding，现在应该有pymilvus-model支持了
    config.set_provider_config(
        "embedding",
        "MilvusEmbedding",
        {
            "model": "sentence-transformers/all-MiniLM-L6-v2",  # 本地模型
        },
    )
    init_config(config=config)

    # Load your local data
    from deepsearcher.offline_loading import load_from_local_files

    load_from_local_files(paths_or_directory="./tmpki79mvp__origin.pdf")

    # (Optional) Load from web crawling (`FIRECRAWL_API_KEY` env variable required)
    # from deepsearcher.offline_loading import load_from_website
    # load_from_website(urls=website_url)

    # Query
    result = query(
        "You need to extract specific information from the PDF including basic information (project name, bidding number, tenderer information, agency), commercial requirements (bidding time, location, bidder qualifications, bid security, performance security, payment terms, price composition, registered capital, qualification grade, performance requirements), technical requirements (technical specifications, performance indicators, quality standards, acceptance criteria, personnel requirements, delivery requirements, maintenance requirements, certification materials), bid rejection items (bid rejection conditions), and scoring items (scoring standards)."
    )  # Your question here
    print(result)


if __name__ == "__main__":
    main()
